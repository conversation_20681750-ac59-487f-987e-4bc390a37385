# BarterBox - Community Skill-Swap Platform

A full-stack platform where users can trade skills without using money (e.g., "I teach guitar, you help me garden").

## 🚀 Tech Stack

- **Backend**: Go (Gorilla Mux)
- **Database**: SQLite with migrations
- **Frontend**: Next.js + Tailwind CSS (coming soon)
- **WebSockets**: Gorilla WebSocket for real-time chat
- **Auth**: JWT for session management, bcrypt for password hashing
- **Deployment**: Docker + Fly.io for backend, Netlify for frontend

## 📁 Project Structure

```
BarterBox/
├── backend/              # Go backend application
│   ├── cmd/server/       # Main application entry point
│   ├── internal/
│   │   ├── config/       # Environment configuration
│   │   ├── database/     # Database connection & migrations
│   │   ├── auth/         # JWT & bcrypt authentication
│   │   ├── middleware/   # HTTP middleware (auth, CORS, etc.)
│   │   ├── logger/       # Structured logging
│   │   ├── routes/       # HTTP route definitions
│   │   ├── models/       # Database models and DTOs
│   │   ├── handlers/     # HTTP request handlers (TODO)
│   │   ├── websocket/    # Real-time chat (TODO)
│   │   └── utils/        # Helper utilities (TODO)
│   ├── bin/              # Compiled binaries
│   ├── go.mod            # Go module definition
│   ├── go.sum            # Go dependency checksums
│   ├── .env              # Environment variables
│   └── barterbox.db      # SQLite database file
├── frontend/             # Next.js frontend (TODO)
├── scripts/              # Database seeding & utilities (TODO)
└── README.md

## ✅ Setup Progress

### Completed ✅
- [x] Go backend structure initialized
- [x] Database schema and migration system
- [x] Environment configuration with .env
- [x] Core dependencies installed (Gorilla Mux, JWT, bcrypt, SQLite)
- [x] Authentication service with JWT and bcrypt
- [x] Middleware for auth, CORS, and admin access
- [x] Database models and DTOs
- [x] Basic server with health check endpoint
- [x] Successful compilation test

### Next Steps 🔄
- [ ] Implement API handlers (auth, users, skills, matches, messages)
- [ ] Set up WebSocket for real-time chat
- [ ] Initialize Next.js frontend
- [ ] Create Docker configuration
- [ ] Add database seeding scripts
- [ ] Test all API endpoints

## 🛠️ Quick Start

### Prerequisites
- Go 1.21+ installed
- Git

### 1. Clone and Setup
```bash
git clone <your-repo-url>
cd BarterBox
```

### 2. Install Dependencies
```bash
cd backend
go mod tidy
```

### 3. Configure Environment
The `.env` file is already created with default values in the `backend/` directory. Update these for production:
```bash
# Important: Change these in production!
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### 4. Build and Run
```bash
# From the backend directory
cd backend

# Build the server
go build -o bin/server ./cmd/server

# Run the server
./bin/server
```

The server will start on `http://localhost:8080`

### 5. Test Health Check
```bash
curl http://localhost:8080/api/health
```

Expected response:
```json
{"status": "healthy", "service": "barterbox-api"}
```

## 📋 API Endpoints (Planned)

### Authentication
- `POST /api/auth/signup` - Create account
- `POST /api/auth/login` - Authenticate and return JWT
- `GET /api/auth/me` - Get current user (requires auth)

### Users
- `GET /api/users/:id` - Get user profile
- `PUT /api/users/:id` - Update profile (auth required)

### Skills
- `POST /api/skills` - Create skill listing (auth required)
- `GET /api/skills` - Get all skills (supports search)
- `GET /api/skills/:id` - Get skill details
- `PUT /api/skills/:id` - Update skill (auth required)
- `DELETE /api/skills/:id` - Delete skill (auth required)

### Matchmaking
- `GET /api/matches` - Get matches for logged-in user

### Messages
- `GET /api/messages/:match_id` - Get chat history
- `POST /api/messages/:match_id` - Send message (auth required)
- `WS /api/ws/chat/:match_id` - Real-time messaging

### Notifications
- `GET /api/notifications` - Get notifications (auth required)
- `PUT /api/notifications/:id/read` - Mark as read

### Admin
- `GET /api/admin/users` - List all users (admin only)
- `DELETE /api/admin/users/:id` - Remove user (admin only)

## 🗄️ Database Schema

The database includes these main tables:
- **users** - User accounts and profiles
- **skills** - Skill listings (offers/needs)
- **matches** - User matches based on complementary skills
- **messages** - Chat messages between matched users
- **notifications** - In-app notifications

## 🔧 Development

### Running in Development Mode
```bash
# From the backend directory
cd backend

# Set environment to development
export ENV=development

# Run with auto-reload (install air first: go install github.com/cosmtrek/air@latest)
air

# Or run directly
go run ./cmd/server
```

### Database Management
```bash
# The database will be automatically created and migrated on first run
# Database file: backend/barterbox.db

# To reset the database, simply delete the file:
rm backend/barterbox.db
```

## 🚀 Next Development Steps

1. **Implement API Handlers** - Create handlers for all planned endpoints
2. **Add WebSocket Support** - Real-time chat functionality
3. **Frontend Setup** - Initialize Next.js with Tailwind CSS
4. **Testing** - Add unit and integration tests
5. **Docker Setup** - Containerize the application
6. **Deployment** - Set up CI/CD with GitHub Actions

## 🤝 Contributing

This is a learning project following production-quality practices. Feel free to contribute!

## 📄 License

MIT License - see LICENSE file for details.
BarterBox is a web app where people in a local community can trade skills without money.
