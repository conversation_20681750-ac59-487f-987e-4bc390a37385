# Server Configuration
PORT=8080
HOST=localhost
ENV=development

# Database Configuration
DB_PATH=./barterbox.db

# JWT Configuration
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRY_HOURS=24

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=

# File Upload Configuration
MAX_FILE_SIZE=5MB
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# WebSocket Configuration
WS_READ_BUFFER_SIZE=1024
WS_WRITE_BUFFER_SIZE=1024

# Security
BCRYPT_COST=12

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Logging Configuration
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_COMPRESS=true
