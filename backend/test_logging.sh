#!/bin/bash

echo "Testing file logging functionality..."

# Start server in background
echo "Starting server..."
./bin/server &
SERVER_PID=$!

# Wait for server to start
sleep 2

# Make a test request
echo "Making test request..."
curl -s http://localhost:8080/api/health > /dev/null

# Wait a moment for logs to be written
sleep 1

# Stop server
echo "Stopping server..."
kill $SERVER_PID
wait $SERVER_PID 2>/dev/null

# Show the log file contents
echo "Log file contents:"
echo "=================="
cat logs/app.log
echo "=================="

echo "Test completed!"
