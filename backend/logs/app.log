time=2025-08-16T15:45:48.794+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:194 msg="Configuration loaded" environment=development
time=2025-08-16T15:45:48.798+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:200 msg="Database connected" db_path=./barterbox.db
time=2025-08-16T15:45:48.798+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:206 msg="Database initialized successfully"
time=2025-08-16T15:45:48.799+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:173 msg="Server starting" address=localhost:8080 environment=development timestamp=2025-08-16T12:45:48.799Z
time=2025-08-16T15:46:14.010+03:00 level=DEBUG source=/home/<USER>/BarterBox/backend/internal/routes/routes.go:92 msg="Health check requested" operation=health_check
time=2025-08-16T15:46:14.010+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:127 msg="HTTP request" method=GET path=/api/health user_agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" remote_addr=127.0.0.1:47096 duration_ms=0 status_code=200
time=2025-08-16T15:51:41.355+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:127 msg="HTTP request" method=GET path=/api/auth/me user_agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" remote_addr=127.0.0.1:47096 duration_ms=0 status_code=401
