time=2025-08-16T15:37:43.659+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:194 msg="Configuration loaded" environment=development
time=2025-08-16T15:37:43.660+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:200 msg="Database connected" db_path=./barterbox.db
time=2025-08-16T15:37:43.661+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:206 msg="Database initialized successfully"
time=2025-08-16T15:37:43.661+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:173 msg="Server starting" address=localhost:8080 environment=development timestamp=2025-08-16T12:37:43.661Z
time=2025-08-16T15:37:48.656+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:181 msg="Server shutting down" timestamp=2025-08-16T12:37:48.656Z
time=2025-08-16T15:37:48.656+03:00 level=INFO source=/home/<USER>/BarterBox/backend/internal/logger/logger.go:187 msg="Server stopped" timestamp=2025-08-16T12:37:48.656Z
