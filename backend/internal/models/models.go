package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID           int       `json:"id" db:"id"`
	Name         string    `json:"name" db:"name"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"` // Don't include in JSON responses
	Bio          *string   `json:"bio" db:"bio"`
	Location     *string   `json:"location" db:"location"`
	ProfileImage *string   `json:"profile_image" db:"profile_image"`
	Role         string    `json:"role" db:"role"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// UserCreateRequest represents the request to create a new user
type UserCreateRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
	Bio      string `json:"bio,omitempty" validate:"max=500"`
	Location string `json:"location,omitempty" validate:"max=100"`
}

// UserUpdateRequest represents the request to update a user
type UserUpdateRequest struct {
	Name         string `json:"name,omitempty" validate:"omitempty,min=2,max=100"`
	Bio          string `json:"bio,omitempty" validate:"max=500"`
	Location     string `json:"location,omitempty" validate:"max=100"`
	ProfileImage string `json:"profile_image,omitempty"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	Token string `json:"token"`
	User  User   `json:"user"`
}

// Skill represents a skill listing
type Skill struct {
	ID          int       `json:"id" db:"id"`
	UserID      int       `json:"user_id" db:"user_id"`
	Title       string    `json:"title" db:"title"`
	Category    *string   `json:"category" db:"category"`
	Description *string   `json:"description" db:"description"`
	SkillType   string    `json:"skill_type" db:"skill_type"` // "offer" or "need"
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	User        *User     `json:"user,omitempty"` // For joined queries
}

// SkillCreateRequest represents the request to create a new skill
type SkillCreateRequest struct {
	Title       string `json:"title" validate:"required,min=3,max=200"`
	Category    string `json:"category,omitempty" validate:"max=50"`
	Description string `json:"description,omitempty" validate:"max=1000"`
	SkillType   string `json:"skill_type" validate:"required,oneof=offer need"`
}

// SkillUpdateRequest represents the request to update a skill
type SkillUpdateRequest struct {
	Title       string `json:"title,omitempty" validate:"omitempty,min=3,max=200"`
	Category    string `json:"category,omitempty" validate:"max=50"`
	Description string `json:"description,omitempty" validate:"max=1000"`
}

// Match represents a match between two users
type Match struct {
	ID        int       `json:"id" db:"id"`
	User1ID   int       `json:"user1_id" db:"user1_id"`
	User2ID   int       `json:"user2_id" db:"user2_id"`
	Score     int       `json:"score" db:"score"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	User1     *User     `json:"user1,omitempty"` // For joined queries
	User2     *User     `json:"user2,omitempty"` // For joined queries
}

// Message represents a chat message
type Message struct {
	ID         int       `json:"id" db:"id"`
	MatchID    int       `json:"match_id" db:"match_id"`
	SenderID   int       `json:"sender_id" db:"sender_id"`
	ReceiverID int       `json:"receiver_id" db:"receiver_id"`
	Content    string    `json:"content" db:"content"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	Sender     *User     `json:"sender,omitempty"`   // For joined queries
	Receiver   *User     `json:"receiver,omitempty"` // For joined queries
}

// MessageCreateRequest represents the request to create a new message
type MessageCreateRequest struct {
	Content string `json:"content" validate:"required,min=1,max=1000"`
}

// Notification represents a user notification
type Notification struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	Type      string    `json:"type" db:"type"`
	Content   string    `json:"content" db:"content"`
	IsRead    bool      `json:"is_read" db:"is_read"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// NotificationCreateRequest represents the request to create a notification
type NotificationCreateRequest struct {
	UserID  int    `json:"user_id" validate:"required"`
	Type    string `json:"type" validate:"required,oneof=match message"`
	Content string `json:"content" validate:"required,min=1,max=500"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// PaginationParams represents pagination parameters
type PaginationParams struct {
	Page     int `json:"page" validate:"min=1"`
	PageSize int `json:"page_size" validate:"min=1,max=100"`
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	Total      int         `json:"total"`
	TotalPages int         `json:"total_pages"`
}
