package logger

import (
	"context"
	"log/slog"
	"os"
	"time"
)

// <PERSON>gger wraps slog.Logger with additional context and convenience methods
type Logger struct {
	*slog.Logger
}

// Config holds logger configuration
type Config struct {
	Level  string // debug, info, warn, error
	Format string // json, text
	Env    string // development, production
}

// New creates a new structured logger based on configuration
func New(config Config) *Logger {
	var level slog.Level
	switch config.Level {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	var handler slog.Handler
	opts := &slog.HandlerOptions{
		Level: level,
		AddSource: config.Env == "development",
	}

	if config.Format == "json" || config.Env == "production" {
		handler = slog.NewJSONHandler(os.Stdout, opts)
	} else {
		handler = slog.NewTextHandler(os.Stdout, opts)
	}

	logger := slog.New(handler)
	return &Logger{Logger: logger}
}

// WithContext adds context to the logger
func (l *Logger) WithContext(ctx context.Context) *Logger {
	return &Logger{Logger: l.Logger.With()}
}

// WithFields adds structured fields to the logger
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	args := make([]interface{}, 0, len(fields)*2)
	for k, v := range fields {
		args = append(args, k, v)
	}
	return &Logger{Logger: l.Logger.With(args...)}
}

// WithRequestID adds request ID to the logger
func (l *Logger) WithRequestID(requestID string) *Logger {
	return &Logger{Logger: l.Logger.With("request_id", requestID)}
}

// WithUserID adds user ID to the logger
func (l *Logger) WithUserID(userID int) *Logger {
	return &Logger{Logger: l.Logger.With("user_id", userID)}
}

// WithOperation adds operation name to the logger
func (l *Logger) WithOperation(operation string) *Logger {
	return &Logger{Logger: l.Logger.With("operation", operation)}
}

// HTTP request logging helpers
func (l *Logger) LogHTTPRequest(method, path, userAgent, remoteAddr string, duration time.Duration, statusCode int) {
	l.Info("HTTP request",
		"method", method,
		"path", path,
		"user_agent", userAgent,
		"remote_addr", remoteAddr,
		"duration_ms", duration.Milliseconds(),
		"status_code", statusCode,
	)
}

// Database operation logging helpers
func (l *Logger) LogDBOperation(operation, table string, duration time.Duration, err error) {
	if err != nil {
		l.Error("Database operation failed",
			"operation", operation,
			"table", table,
			"duration_ms", duration.Milliseconds(),
			"error", err.Error(),
		)
	} else {
		l.Debug("Database operation completed",
			"operation", operation,
			"table", table,
			"duration_ms", duration.Milliseconds(),
		)
	}
}

// Auth operation logging helpers
func (l *Logger) LogAuthAttempt(operation, email string, success bool, err error) {
	if success {
		l.Info("Authentication successful",
			"operation", operation,
			"email", email,
		)
	} else {
		l.Warn("Authentication failed",
			"operation", operation,
			"email", email,
			"error", err.Error(),
		)
	}
}

// Server lifecycle logging helpers
func (l *Logger) LogServerStart(address, env string) {
	l.Info("Server starting",
		"address", address,
		"environment", env,
		"timestamp", time.Now().UTC(),
	)
}

func (l *Logger) LogServerShutdown() {
	l.Info("Server shutting down",
		"timestamp", time.Now().UTC(),
	)
}

func (l *Logger) LogServerStop() {
	l.Info("Server stopped",
		"timestamp", time.Now().UTC(),
	)
}

// Configuration logging helpers
func (l *Logger) LogConfigLoad(env string) {
	l.Info("Configuration loaded",
		"environment", env,
	)
}

func (l *Logger) LogDatabaseConnection(dbPath string) {
	l.Info("Database connected",
		"db_path", dbPath,
	)
}

func (l *Logger) LogDatabaseInit() {
	l.Info("Database initialized successfully")
}

// Error logging with context
func (l *Logger) LogError(operation string, err error, fields map[string]interface{}) {
	args := []interface{}{
		"operation", operation,
		"error", err.Error(),
	}
	
	for k, v := range fields {
		args = append(args, k, v)
	}
	
	l.Error("Operation failed", args...)
}

// Fatal logging with context
func (l *Logger) LogFatal(operation string, err error, fields map[string]interface{}) {
	args := []interface{}{
		"operation", operation,
		"error", err.Error(),
	}
	
	for k, v := range fields {
		args = append(args, k, v)
	}
	
	l.Error("Fatal error", args...)
	os.Exit(1)
}
