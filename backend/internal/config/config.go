package config

import (
	"log/slog"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	// Server Configuration
	Port string
	Host string
	Env  string

	// Database Configuration
	DBPath string

	// JWT Configuration
	JWTSecret      string
	JWTExpiryHours int

	// CORS Configuration
	CORSAllowedOrigins []string

	// Email Configuration
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string

	// File Upload Configuration
	MaxFileSize string
	UploadDir   string

	// Rate Limiting
	RateLimitRequests int
	RateLimitWindow   int

	// WebSocket Configuration
	WSReadBufferSize  int
	WSWriteBufferSize int

	// Security
	BcryptCost int

	// Frontend URL
	FrontendURL string

	// Logging Configuration
	LogFilePath   string
	LogMaxSize    int // megabytes
	LogMaxBackups int
	LogMaxAge     int // days
	LogCompress   bool
}

// Load loads configuration from environment variables
func Load() *Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		slog.Info("No .env file found, using environment variables")
	}

	config := &Config{
		// Server Configuration
		Port: getEnv("PORT", "8080"),
		Host: getEnv("HOST", "localhost"),
		Env:  getEnv("ENV", "development"),

		// Database Configuration
		DBPath: getEnv("DB_PATH", "./barterbox.db"),

		// JWT Configuration
		JWTSecret:      getEnv("JWT_SECRET", "default-secret-change-in-production"),
		JWTExpiryHours: getEnvAsInt("JWT_EXPIRY_HOURS", 24),

		// CORS Configuration
		CORSAllowedOrigins: getEnvAsSlice("CORS_ALLOWED_ORIGINS", []string{"http://localhost:3000"}),

		// Email Configuration
		SMTPHost:     getEnv("SMTP_HOST", "smtp.gmail.com"),
		SMTPPort:     getEnvAsInt("SMTP_PORT", 587),
		SMTPUsername: getEnv("SMTP_USERNAME", ""),
		SMTPPassword: getEnv("SMTP_PASSWORD", ""),

		// File Upload Configuration
		MaxFileSize: getEnv("MAX_FILE_SIZE", "5MB"),
		UploadDir:   getEnv("UPLOAD_DIR", "./uploads"),

		// Rate Limiting
		RateLimitRequests: getEnvAsInt("RATE_LIMIT_REQUESTS", 100),
		RateLimitWindow:   getEnvAsInt("RATE_LIMIT_WINDOW", 60),

		// WebSocket Configuration
		WSReadBufferSize:  getEnvAsInt("WS_READ_BUFFER_SIZE", 1024),
		WSWriteBufferSize: getEnvAsInt("WS_WRITE_BUFFER_SIZE", 1024),

		// Security
		BcryptCost: getEnvAsInt("BCRYPT_COST", 12),

		// Frontend URL
		FrontendURL: getEnv("FRONTEND_URL", "http://localhost:3000"),

		// Logging Configuration
		LogFilePath:   getEnv("LOG_FILE_PATH", "logs/app.log"),
		LogMaxSize:    getEnvAsInt("LOG_MAX_SIZE", 100),   // 100MB
		LogMaxBackups: getEnvAsInt("LOG_MAX_BACKUPS", 3),  // Keep 3 backup files
		LogMaxAge:     getEnvAsInt("LOG_MAX_AGE", 28),     // Keep logs for 28 days
		LogCompress:   getEnvAsBool("LOG_COMPRESS", true), // Compress rotated logs
	}

	// Validate required configuration
	if config.JWTSecret == "default-secret-change-in-production" && config.Env == "production" {
		slog.Error("JWT_SECRET must be set in production environment")
		os.Exit(1)
	}

	return config
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		slog.Warn("Invalid integer value for environment variable, using fallback",
			"key", key,
			"value", value,
			"fallback", fallback,
		)
	}
	return fallback
}

// getEnvAsSlice gets an environment variable as slice with a fallback value
func getEnvAsSlice(key string, fallback []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return fallback
}

// getEnvAsBool gets an environment variable as boolean with a fallback value
func getEnvAsBool(key string, fallback bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
		slog.Warn("Invalid boolean value for environment variable, using fallback",
			"key", key,
			"value", value,
			"fallback", fallback,
		)
	}
	return fallback
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Env == "development"
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Env == "production"
}

// GetServerAddress returns the full server address
func (c *Config) GetServerAddress() string {
	return c.Host + ":" + c.Port
}
