package routes

import (
	"net/http"

	"github.com/gorilla/mux"

	"barterbox/backend/internal/auth"
	"barterbox/backend/internal/database"
	"barterbox/backend/internal/logger"
	"barterbox/backend/internal/middleware"
)

// Dependencies holds all the dependencies needed for route handlers
type Dependencies struct {
	DB          *database.DB
	AuthService *auth.AuthService
	Logger      *logger.Logger
}

// SetupRoutes configures all HTTP routes and returns the configured router
func SetupRoutes(deps *Dependencies, corsAllowedOrigins []string) *mux.Router {
	// Create router
	router := mux.NewRouter()

	// Apply global middleware
	router.Use(middleware.CORSMiddleware(corsAllowedOrigins))
	router.Use(middleware.LoggingMiddleware(deps.Logger))

	// API routes
	api := router.PathPrefix("/api").Subrouter()

	// Setup public routes
	setupPublicRoutes(api, deps)

	// Setup auth routes
	setupAuthRoutes(api, deps)

	// Setup protected routes
	setupProtectedRoutes(api, deps)

	return router
}

// setupPublicRoutes configures routes that don't require authentication
func setupPublicRoutes(api *mux.Router, deps *Dependencies) {
	api.HandleFunc("/health", healthCheckHandler(deps)).Methods("GET")
}

// setupAuthRoutes configures authentication-related routes
func setupAuthRoutes(api *mux.Router, deps *Dependencies) {
	authRouter := api.PathPrefix("/auth").Subrouter()

	authRouter.HandleFunc("/signup", signupHandler(deps)).Methods("POST")
	authRouter.HandleFunc("/login", loginHandler(deps)).Methods("POST")
}

// setupProtectedRoutes configures routes that require authentication
func setupProtectedRoutes(api *mux.Router, deps *Dependencies) {
	// Protected routes (authentication required)
	protected := api.PathPrefix("").Subrouter()
	protected.Use(middleware.AuthMiddleware(deps.AuthService))

	// User routes
	protected.HandleFunc("/auth/me", getCurrentUserHandler(deps)).Methods("GET")

	// Skills routes
	setupSkillsRoutes(protected, deps)

	// Admin routes
	setupAdminRoutes(protected, deps)
}

// setupSkillsRoutes configures skill-related routes
func setupSkillsRoutes(protected *mux.Router, deps *Dependencies) {
	skillsRouter := protected.PathPrefix("/skills").Subrouter()
	skillsRouter.HandleFunc("", skillsHandler(deps)).Methods("GET", "POST")
}

// setupAdminRoutes configures admin-only routes
func setupAdminRoutes(protected *mux.Router, deps *Dependencies) {
	adminRouter := protected.PathPrefix("/admin").Subrouter()
	adminRouter.Use(middleware.AdminMiddleware)
	adminRouter.HandleFunc("/users", adminUsersHandler(deps)).Methods("GET")
}

// Handler functions

// healthCheckHandler handles health check requests
func healthCheckHandler(deps *Dependencies) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		deps.Logger.WithOperation("health_check").Debug("Health check requested")

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "healthy", "service": "barterbox-api"}`))
	}
}

// signupHandler handles user signup requests
func signupHandler(deps *Dependencies) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		deps.Logger.WithOperation("signup").Info("Signup endpoint called")

		// TODO: Implement signup handler
		w.WriteHeader(http.StatusNotImplemented)
		w.Write([]byte(`{"message": "Signup endpoint - coming soon"}`))
	}
}

// loginHandler handles user login requests
func loginHandler(deps *Dependencies) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		deps.Logger.WithOperation("login").Info("Login endpoint called")

		// TODO: Implement login handler
		w.WriteHeader(http.StatusNotImplemented)
		w.Write([]byte(`{"message": "Login endpoint - coming soon"}`))
	}
}

// getCurrentUserHandler handles get current user requests
func getCurrentUserHandler(deps *Dependencies) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		deps.Logger.WithOperation("get_current_user").Info("Get current user endpoint called")

		// TODO: Implement get current user handler
		w.WriteHeader(http.StatusNotImplemented)
		w.Write([]byte(`{"message": "Get current user endpoint - coming soon"}`))
	}
}

// skillsHandler handles skills-related requests
func skillsHandler(deps *Dependencies) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		deps.Logger.WithOperation("skills").Info("Skills endpoint called", "method", r.Method)

		// TODO: Implement skills handlers
		w.WriteHeader(http.StatusNotImplemented)
		w.Write([]byte(`{"message": "Skills endpoints - coming soon"}`))
	}
}

// adminUsersHandler handles admin user management requests
func adminUsersHandler(deps *Dependencies) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		deps.Logger.WithOperation("admin_users").Info("Admin users endpoint called")

		// TODO: Implement admin handlers
		w.WriteHeader(http.StatusNotImplemented)
		w.Write([]byte(`{"message": "Admin endpoints - coming soon"}`))
	}
}
