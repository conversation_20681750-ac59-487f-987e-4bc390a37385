package main

import (
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"barterbox/backend/internal/auth"
	"barterbox/backend/internal/config"
	"barterbox/backend/internal/database"
	"barterbox/backend/internal/logger"
	"barterbox/backend/internal/routes"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize structured logger
	loggerConfig := logger.Config{
		Level:  getLogLevel(cfg.Env),
		Format: getLogFormat(cfg.Env),
		Env:    cfg.Env,
	}
	appLogger := logger.New(loggerConfig)
	appLogger.LogConfigLoad(cfg.Env)

	// Initialize database
	db, err := database.NewConnection(cfg.DBPath)
	if err != nil {
		appLogger.LogFatal("database_connection", err, map[string]interface{}{
			"db_path": cfg.DBPath,
		})
	}
	defer db.Close()
	appLogger.LogDatabaseConnection(cfg.DBPath)

	// Initialize database schema
	if err := db.InitializeDatabase(); err != nil {
		appLogger.LogFatal("database_initialization", err, nil)
	}
	appLogger.LogDatabaseInit()

	// Initialize auth service
	authService := auth.NewAuthService(cfg.JWTSecret, cfg.JWTExpiryHours, cfg.BcryptCost)

	// Setup route dependencies
	deps := &routes.Dependencies{
		DB:          db,
		AuthService: authService,
		Logger:      appLogger,
	}

	// Setup routes
	router := routes.SetupRoutes(deps, cfg.CORSAllowedOrigins)

	// Start server
	server := &http.Server{
		Addr:    cfg.GetServerAddress(),
		Handler: router,
	}

	// Graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		appLogger.LogServerShutdown()
		if err := server.Close(); err != nil {
			appLogger.LogError("server_shutdown", err, nil)
		}
	}()

	appLogger.LogServerStart(cfg.GetServerAddress(), cfg.Env)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		appLogger.LogFatal("server_start", err, map[string]interface{}{
			"address": cfg.GetServerAddress(),
		})
	}

	appLogger.LogServerStop()
}

// getLogLevel returns the appropriate log level based on environment
func getLogLevel(env string) string {
	switch env {
	case "production":
		return "info"
	case "development":
		return "debug"
	default:
		return "info"
	}
}

// getLogFormat returns the appropriate log format based on environment
func getLogFormat(env string) string {
	switch env {
	case "production":
		return "json"
	case "development":
		return "text"
	default:
		return "text"
	}
}
